{"rustc": 1842507548689473721, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 6120535526081445694, "profile": 15657897354478470176, "path": 17920806599311208807, "deps": [[5700553729601021974, "futures_util", false, 3141641522956284237], [6323537352664534935, "futures_io", false, 18001422241586287730], [7743984271493053878, "futures_core", false, 2936818096815362250], [9961102183257020711, "futures_task", false, 15878256687754208253], [16078276589449426613, "futures_channel", false, 5272287514293821295], [18183901555165856195, "futures_sink", false, 1387415848769325537], [18216287767803176676, "futures_executor", false, 4295436657741706395]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-101b4e47ef6ba1b9\\dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}