{"rustc": 1842507548689473721, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"full\", \"parsing\", \"printing\", \"proc-macro\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 2225463790103693989, "path": 17212183120498275042, "deps": [[3060637413840920116, "proc_macro2", false, 16984576690893494466], [10418434610764581512, "unicode_ident", false, 12154141632640805922], [17990358020177143287, "quote", false, 7594615570981054347]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\syn-ce8015f03ccea218\\dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}