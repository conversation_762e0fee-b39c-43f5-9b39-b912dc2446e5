{"rustc": 1842507548689473721, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"channel\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 6953988541840603879, "profile": 15657897354478470176, "path": 8693366195862988595, "deps": [[1615478164327904835, "pin_utils", false, 14445859001125264220], [4119161710677519185, "memchr", false, 14265697221195499125], [5700553729601021974, "build_script_build", false, 13228642933608111576], [6323537352664534935, "futures_io", false, 18001422241586287730], [6955678925937229351, "slab", false, 3079828821195502691], [7743984271493053878, "futures_core", false, 2936818096815362250], [9961102183257020711, "futures_task", false, 15878256687754208253], [13790135811457663478, "futures_macro", false, 9906282485790348938], [16078276589449426613, "futures_channel", false, 5272287514293821295], [18183901555165856195, "futures_sink", false, 1387415848769325537], [18307711753340005737, "pin_project_lite", false, 4483423715066127864]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-9d56e139cf40f28e\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}