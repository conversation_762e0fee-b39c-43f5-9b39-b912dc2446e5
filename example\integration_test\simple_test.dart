import 'package:integration_test/integration_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_plugin_rs_test/flutter_plugin_rs_test.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  setUpAll(() async => await RustLib.init());
  test('Can call rust function', () async {
    expect(greet(name: "<PERSON>"), "Hello, <PERSON>!");
  });

  test('open non-exist port', () async {
    var port = FlutterRs(portName: "COM222", baudRate: 1111);
    await port.open();
  });
}
