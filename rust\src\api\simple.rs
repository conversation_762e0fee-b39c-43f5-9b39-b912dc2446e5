use std::{
    io,
    sync::{<PERSON>, Mutex},
    time::Duration,
};

use flutter_rust_bridge::frb;
use serialport::SerialPort;

#[flutter_rust_bridge::frb(sync)] // Synchronous mode for simplicity of the demo
pub fn greet(name: String) -> String {
    format!("Hello, {name}!")
}

#[flutter_rust_bridge::frb(init)]
pub fn init_app() {
    // Default utilities - feel free to customize
    flutter_rust_bridge::setup_default_user_utils();
}

#[frb(unignore)]
pub struct MyStruct {
    pub name: String,
    pub age: u32,
}

impl MyStruct {
    #[frb(sync)]
    pub fn new(name: String, age: u32) -> Self {
        Self { name, age }
    }

    #[frb] // This allows the method to be called synchronously from Dart
    pub fn greet(&self) -> String {
        format!(
            "Hello, my name is {} and I am {} years old.",
            self.name, self.age
        )
    }
}
#[frb(opaque)]
pub struct FlutterRs {
    pub port_name: String,
    pub baud_rate: u32,
    // pub data_bits: u8,
    // pub stop_bits: u8,
    // pub parity: u8,

    // private fields
    _port: Option<Arc<SafeSerialPort>>,
}

impl FlutterRs {
    #[frb(sync)]
    pub fn new(
        port_name: String,
        baud_rate: u32,
        // data_bits: u8,
        // stop_bits: u8,
        // parity: u8,
    ) -> Self {
        Self {
            port_name,
            baud_rate,
            // data_bits,
            // stop_bits,
            // parity,
            _port: None,
        }
    }

    #[frb]
    pub fn open(&mut self) -> io::Result<()> {
        let safe_port = SafeSerialPort::new(&self.port_name, self.baud_rate);
        safe_port.open()?; // 显式调用 open 方法
        self._port = Some(Arc::new(safe_port));
        Ok(())
    }

    #[frb]
    pub fn read(&self) -> io::Result<Vec<u8>> {
        let port = self._port.as_ref().ok_or_else(|| {
            io::Error::new(io::ErrorKind::NotConnected, "Serial port is not open")
        })?;

        let mut buff = vec![0; 1024];

        match port.read(&mut buff) {
            Ok(size) => Ok(buff[..size].to_vec()),
            Err(ref e) if e.kind() == io::ErrorKind::TimedOut => Ok(vec![]),
            Err(e) => Err(e),
        }
    }

    #[frb]
    pub fn write(&self, output: Vec<u8>) -> io::Result<usize> {
        let port = self._port.as_ref().ok_or_else(|| {
            io::Error::new(io::ErrorKind::NotConnected, "Serial port is not open")
        })?;

        port.write(&output)
    }

    #[frb]
    pub fn close(&self) -> io::Result<()> {
        let port = self._port.as_ref().ok_or_else(|| {
            io::Error::new(io::ErrorKind::NotConnected, "Serial port is not open")
        })?;

        port.close()
    }

    #[frb]
    pub fn is_connected(&self) -> bool {
        match &self._port {
            Some(port) => port.is_connected(),
            None => false,
        }
    }
}

#[cfg(test)]
mod tests {

    use chrono::Local;

    use super::*;

    #[test]
    fn test_greet() {
        assert_eq!(greet("Tom".to_string()), "Hello, Tom!");
    }

    #[test]
    fn test_flutter_rs() {
        let mut rs = FlutterRs::new("COM15".to_string(), 115200);
        rs.open().unwrap();

        for _ in 0..100 {
            let buf = rs.read().unwrap();
            println!("time {:?} {:?}", Local::now(), buf);
        }
    }

    #[test]
    fn test_flutter_rs_close() {
        let mut rs = FlutterRs::new("COM15".to_string(), 115200);
        rs.open().unwrap();

        rs.close().unwrap();

        assert!(rs.write(vec![1, 2, 3]).is_err());
    }

    #[test]
    fn test_safe() {
        let port = SafeSerialPort::new("COM15", 115200);
        // 测试创建 SafeSerialPort 实例（此时应该未连接）
        assert!(!port.is_connected());
        println!("SafeSerialPort created successfully (not connected)");

        // 测试打开串口（可能会失败，因为没有实际硬件）
        match port.open() {
            Ok(_) => {
                println!("Serial port opened successfully");
                assert!(port.is_connected());
            }
            Err(e) => {
                println!(
                    "Failed to open serial port (expected without hardware): {}",
                    e
                );
                assert!(!port.is_connected());
            }
        }
    }

    #[test]
    fn test_thread_safety() {
        use std::sync::Arc;
        use std::thread;
        use std::time::Duration;

        let port = Arc::new(SafeSerialPort::new("COM15", 115200));

        // 尝试打开串口（可能会失败，但不影响线程安全测试）
        let _ = port.open();

        let mut handles = vec![];

        // 创建多个读线程
        for i in 0..3 {
            let port_clone = Arc::clone(&port);
            let handle = thread::spawn(move || {
                let mut buf = vec![0; 64];
                for _j in 0..5 {
                    match port_clone.read(&mut buf) {
                        Ok(size) => println!("Read thread {}: read {} bytes", i, size),
                        Err(e) => println!("Read thread {}: error {}", i, e),
                    }
                    thread::sleep(Duration::from_millis(10));
                }
            });
            handles.push(handle);
        }

        // 创建多个写线程
        for i in 0..3 {
            let port_clone = Arc::clone(&port);
            let handle = thread::spawn(move || {
                let data = vec![i as u8; 8];
                for _j in 0..5 {
                    match port_clone.write(&data) {
                        Ok(size) => println!("Write thread {}: wrote {} bytes", i, size),
                        Err(e) => println!("Write thread {}: error {}", i, e),
                    }
                    thread::sleep(Duration::from_millis(10));
                }
            });
            handles.push(handle);
        }

        // 等待所有线程完成
        for handle in handles {
            handle.join().unwrap();
        }

        println!("Thread safety test completed");
    }

    // ==================== 异常情况测试 ====================

    #[test]
    fn test_closed_port_operations() {
        println!("=== 测试串口关闭后的操作 ===");

        let port = SafeSerialPort::new("COM99", 115200); // 使用不存在的端口

        // 尝试打开（可能失败）
        let _ = port.open();

        // 关闭串口
        let close_result = port.close();
        println!("Close result: {:?}", close_result);

        // 验证状态
        assert!(
            !port.is_connected(),
            "Port should be disconnected after close"
        );

        // 测试关闭后的读操作
        let mut buf = vec![0; 64];
        match port.read(&mut buf) {
            Err(e) => {
                println!("Read after close - Error (expected): {}", e);
                assert_eq!(e.kind(), io::ErrorKind::NotConnected);
            }
            Ok(_) => panic!("Read should fail after port is closed"),
        }

        // 测试关闭后的写操作
        let data = vec![1, 2, 3, 4];
        match port.write(&data) {
            Err(e) => {
                println!("Write after close - Error (expected): {}", e);
                assert_eq!(e.kind(), io::ErrorKind::NotConnected);
            }
            Ok(_) => panic!("Write should fail after port is closed"),
        }

        println!("✅ 串口关闭后操作测试通过");
    }

    #[test]
    fn test_duplicate_operations() {
        println!("=== 测试重复操作 ===");

        let port = SafeSerialPort::new("COM99", 115200);

        // 测试重复打开
        let first_open = port.open();
        let second_open = port.open();
        let third_open = port.open();

        println!("First open: {:?}", first_open);
        println!("Second open: {:?}", second_open);
        println!("Third open: {:?}", third_open);

        // 重复打开应该成功（幂等操作）
        assert!(second_open.is_ok(), "Second open should succeed");
        assert!(third_open.is_ok(), "Third open should succeed");

        // 测试重复关闭
        let first_close = port.close();
        let second_close = port.close();
        let third_close = port.close();

        println!("First close: {:?}", first_close);
        println!("Second close: {:?}", second_close);
        println!("Third close: {:?}", third_close);

        // 重复关闭应该成功（幂等操作）
        assert!(first_close.is_ok(), "First close should succeed");
        assert!(second_close.is_ok(), "Second close should succeed");
        assert!(third_close.is_ok(), "Third close should succeed");

        // 验证最终状态
        assert!(
            !port.is_connected(),
            "Port should be disconnected after close"
        );

        println!("✅ 重复操作测试通过");
    }

    #[test]
    fn test_unopened_port_operations() {
        println!("=== 测试未打开串口的操作 ===");

        let port = SafeSerialPort::new("COM99", 115200);

        // 验证初始状态
        assert!(!port.is_connected(), "New port should not be connected");

        // 测试未打开时的读操作
        let mut buf = vec![0; 64];
        match port.read(&mut buf) {
            Err(e) => {
                println!("Read without open - Error (expected): {}", e);
                assert_eq!(e.kind(), io::ErrorKind::NotConnected);
            }
            Ok(_) => panic!("Read should fail when port is not opened"),
        }

        // 测试未打开时的写操作
        let data = vec![1, 2, 3, 4];
        match port.write(&data) {
            Err(e) => {
                println!("Write without open - Error (expected): {}", e);
                assert_eq!(e.kind(), io::ErrorKind::NotConnected);
            }
            Ok(_) => panic!("Write should fail when port is not opened"),
        }

        // 测试未打开时的关闭操作（应该成功）
        let close_result = port.close();
        println!("Close without open: {:?}", close_result);
        assert!(
            close_result.is_ok(),
            "Close should succeed even when not opened"
        );

        println!("✅ 未打开串口操作测试通过");
    }

    #[test]
    fn test_boundary_conditions() {
        println!("=== 测试边界条件 ===");

        let port = SafeSerialPort::new("COM99", 115200);
        let _ = port.open(); // 尝试打开

        // 测试空读缓冲区
        let mut empty_buf = vec![];
        match port.read(&mut empty_buf) {
            Ok(size) => {
                println!("Read with empty buffer: {} bytes", size);
                assert_eq!(size, 0, "Should read 0 bytes into empty buffer");
            }
            Err(e) => {
                println!("Read with empty buffer - Error: {}", e);
                // 这是可接受的行为
            }
        }

        // 测试空写缓冲区
        let empty_data = vec![];
        match port.write(&empty_data) {
            Ok(size) => {
                println!("Write empty buffer: {} bytes", size);
                assert_eq!(size, 0, "Should write 0 bytes from empty buffer");
            }
            Err(e) => {
                println!("Write empty buffer - Error: {}", e);
                // 这也是可接受的行为
            }
        }

        // 测试大缓冲区
        let large_buf_size = 1024 * 1024; // 1MB
        let mut large_read_buf = vec![0; large_buf_size];
        match port.read(&mut large_read_buf) {
            Ok(size) => println!("Read large buffer: {} bytes", size),
            Err(e) => println!("Read large buffer - Error: {}", e),
        }

        let large_write_data = vec![0xAA; large_buf_size];
        match port.write(&large_write_data) {
            Ok(size) => println!("Write large buffer: {} bytes", size),
            Err(e) => println!("Write large buffer - Error: {}", e),
        }

        println!("✅ 边界条件测试通过");
    }

    #[test]
    fn test_concurrent_exception_scenarios() {
        println!("=== 测试并发异常情况 ===");

        use std::sync::atomic::{AtomicUsize, Ordering};
        use std::sync::Arc;
        use std::thread;
        use std::time::Duration;

        let port = Arc::new(SafeSerialPort::new("COM99", 115200));
        let error_count = Arc::new(AtomicUsize::new(0));
        let success_count = Arc::new(AtomicUsize::new(0));

        // 测试场景1：一个线程关闭串口，其他线程尝试读写
        println!("--- 场景1：关闭与读写并发 ---");
        {
            let port_clone = Arc::clone(&port);
            let _ = port_clone.open(); // 尝试打开

            let mut handles = vec![];

            // 创建读线程
            for _i in 0..3 {
                let port_clone = Arc::clone(&port);
                let error_count_clone = Arc::clone(&error_count);
                let success_count_clone = Arc::clone(&success_count);

                let handle = thread::spawn(move || {
                    for _j in 0..10 {
                        let mut buf = vec![0; 32];
                        match port_clone.read(&mut buf) {
                            Ok(_) => success_count_clone.fetch_add(1, Ordering::Relaxed),
                            Err(_) => error_count_clone.fetch_add(1, Ordering::Relaxed),
                        };
                        thread::sleep(Duration::from_millis(1));
                    }
                });
                handles.push(handle);
            }

            // 创建写线程
            for i in 0..3 {
                let port_clone = Arc::clone(&port);
                let error_count_clone = Arc::clone(&error_count);
                let success_count_clone = Arc::clone(&success_count);

                let handle = thread::spawn(move || {
                    let data = vec![i as u8; 16];
                    for _j in 0..10 {
                        match port_clone.write(&data) {
                            Ok(_) => success_count_clone.fetch_add(1, Ordering::Relaxed),
                            Err(_) => error_count_clone.fetch_add(1, Ordering::Relaxed),
                        };
                        thread::sleep(Duration::from_millis(1));
                    }
                });
                handles.push(handle);
            }

            // 关闭线程
            let port_clone = Arc::clone(&port);
            let handle = thread::spawn(move || {
                thread::sleep(Duration::from_millis(5)); // 让读写线程先开始
                for _i in 0..5 {
                    let _ = port_clone.close();
                    thread::sleep(Duration::from_millis(2));
                }
            });
            handles.push(handle);

            // 等待所有线程完成
            for handle in handles {
                handle.join().unwrap();
            }
        }

        println!(
            "场景1 - 成功操作: {}, 错误操作: {}",
            success_count.load(Ordering::Relaxed),
            error_count.load(Ordering::Relaxed)
        );

        // 重置计数器
        error_count.store(0, Ordering::Relaxed);
        success_count.store(0, Ordering::Relaxed);

        // 测试场景2：多个线程同时尝试打开/关闭串口
        println!("--- 场景2：多线程打开/关闭 ---");
        {
            let mut handles = vec![];

            // 创建多个打开/关闭线程
            for _i in 0..5 {
                let port_clone = Arc::clone(&port);
                let error_count_clone = Arc::clone(&error_count);
                let success_count_clone = Arc::clone(&success_count);

                let handle = thread::spawn(move || {
                    for _j in 0..10 {
                        // 尝试打开
                        match port_clone.open() {
                            Ok(_) => success_count_clone.fetch_add(1, Ordering::Relaxed),
                            Err(_) => error_count_clone.fetch_add(1, Ordering::Relaxed),
                        };

                        thread::sleep(Duration::from_millis(1));

                        // 尝试关闭
                        match port_clone.close() {
                            Ok(_) => success_count_clone.fetch_add(1, Ordering::Relaxed),
                            Err(_) => error_count_clone.fetch_add(1, Ordering::Relaxed),
                        };

                        thread::sleep(Duration::from_millis(1));
                    }
                });
                handles.push(handle);
            }

            // 等待所有线程完成
            for handle in handles {
                handle.join().unwrap();
            }
        }

        println!(
            "场景2 - 成功操作: {}, 错误操作: {}",
            success_count.load(Ordering::Relaxed),
            error_count.load(Ordering::Relaxed)
        );

        // 验证最终状态一致性
        let final_state = port.is_connected();
        println!("最终连接状态: {}", final_state);

        println!("✅ 并发异常情况测试通过");
    }

    #[test]
    fn test_error_message_accuracy() {
        println!("=== 测试错误消息准确性 ===");

        let port = SafeSerialPort::new("COM99", 115200);

        // 测试未打开时的错误消息
        let mut buf = vec![0; 32];
        match port.read(&mut buf) {
            Err(e) => {
                println!("未打开读取错误: {}", e);
                assert_eq!(e.kind(), io::ErrorKind::NotConnected);
                assert!(e.to_string().contains("not open"));
            }
            Ok(_) => panic!("Should fail when not opened"),
        }

        let data = vec![1, 2, 3];
        match port.write(&data) {
            Err(e) => {
                println!("未打开写入错误: {}", e);
                assert_eq!(e.kind(), io::ErrorKind::NotConnected);
                assert!(e.to_string().contains("not open"));
            }
            Ok(_) => panic!("Should fail when not opened"),
        }

        // 尝试打开不存在的端口
        match port.open() {
            Err(e) => {
                println!("打开不存在端口错误: {}", e);
                // 错误类型可能因系统而异
            }
            Ok(_) => {
                println!("端口打开成功（可能是虚拟端口）");

                // 关闭后再测试
                port.close().unwrap();

                match port.read(&mut buf) {
                    Err(e) => {
                        println!("关闭后读取错误: {}", e);
                        assert_eq!(e.kind(), io::ErrorKind::NotConnected);
                    }
                    Ok(_) => panic!("Should fail after close"),
                }
            }
        }

        println!("✅ 错误消息准确性测试通过");
    }

    #[test]
    fn test_resource_cleanup() {
        println!("=== 测试资源清理 ===");

        // 创建多个端口实例，测试资源是否正确清理
        for i in 0..10 {
            let port = SafeSerialPort::new(&format!("COM{}", 90 + i), 115200);
            let _ = port.open();

            // 进行一些操作
            let mut buf = vec![0; 32];
            let _ = port.read(&mut buf);
            let _ = port.write(&[1, 2, 3]);

            // 显式关闭
            let _ = port.close();

            // 验证状态
            assert!(!port.is_connected(), "Port {} should be disconnected", i);
        }

        // 测试 Drop 时的资源清理
        {
            let port = SafeSerialPort::new("COM98", 115200);
            let _ = port.open();
            // port 在这里会被 drop，应该自动清理资源
        }

        println!("✅ 资源清理测试通过");
    }
}

struct SafeSerialPort {
    inner: Arc<Mutex<Option<Box<dyn SerialPort>>>>,
    read_lock: Arc<Mutex<()>>,
    write_lock: Arc<Mutex<()>>,
    is_open: Arc<Mutex<bool>>,
    name: String,
    baud: u32,
}

impl SafeSerialPort {
    fn new(name: &str, baud: u32) -> Self {
        Self {
            inner: Arc::new(Mutex::new(None)),
            read_lock: Arc::new(Mutex::new(())),
            write_lock: Arc::new(Mutex::new(())),
            is_open: Arc::new(Mutex::new(false)),
            name: name.to_string(),
            baud,
        }
    }

    fn open(&self) -> io::Result<()> {
        // 获取写锁和读锁，确保没有其他操作在进行
        let _read_guard = self
            .read_lock
            .lock()
            .map_err(|_| io::Error::new(io::ErrorKind::Other, "Failed to acquire read lock"))?;
        let _write_guard = self
            .write_lock
            .lock()
            .map_err(|_| io::Error::new(io::ErrorKind::Other, "Failed to acquire write lock"))?;

        // 检查是否已经打开
        {
            let is_open = self.is_open.lock().map_err(|_| {
                io::Error::new(io::ErrorKind::Other, "Failed to acquire is_open lock")
            })?;
            if *is_open {
                return Ok(()); // 已经打开，直接返回
            }
        }

        // 创建串口连接
        let port = serialport::new(&self.name, self.baud)
            .timeout(Duration::from_millis(1000))
            .open()?;

        // 存储串口对象
        {
            let mut inner = self.inner.lock().map_err(|_| {
                io::Error::new(io::ErrorKind::Other, "Failed to acquire inner lock")
            })?;
            *inner = Some(port);
        }

        // 更新状态
        {
            let mut is_open = self.is_open.lock().map_err(|_| {
                io::Error::new(io::ErrorKind::Other, "Failed to acquire is_open lock")
            })?;
            *is_open = true;
        }

        Ok(())
    }

    // 只检查逻辑状态，不获取 inner 锁
    fn check_logical_connection(&self) -> io::Result<()> {
        let is_open = self
            .is_open
            .lock()
            .map_err(|_| io::Error::new(io::ErrorKind::Other, "Failed to acquire is_open lock"))?;

        if !*is_open {
            return Err(io::Error::new(
                io::ErrorKind::NotConnected,
                "Serial port is not open",
            ));
        }

        Ok(())
    }

    fn read(&self, buf: &mut [u8]) -> io::Result<usize> {
        // 获取读锁，确保读操作互斥
        let _read_guard = self
            .read_lock
            .lock()
            .map_err(|_| io::Error::new(io::ErrorKind::Other, "Failed to acquire read lock"))?;

        // 检查逻辑连接状态
        self.check_logical_connection()?;

        // 执行读操作
        let mut inner = self
            .inner
            .lock()
            .map_err(|_| io::Error::new(io::ErrorKind::Other, "Failed to acquire inner lock"))?;

        match inner.as_mut() {
            Some(port) => {
                match port.read(buf) {
                    Ok(size) => Ok(size),
                    Err(e) => {
                        // 检查是否是连接断开的错误
                        if self.is_connection_error(&e) {
                            // 释放 inner 锁后再调用 mark_disconnected
                            drop(inner);
                            self.mark_disconnected()?;
                            Err(io::Error::new(
                                io::ErrorKind::NotConnected,
                                format!("Serial port disconnected: {}", e),
                            ))
                        } else {
                            Err(e)
                        }
                    }
                }
            }
            None => Err(io::Error::new(
                io::ErrorKind::NotConnected,
                "Serial port is not available",
            )),
        }
    }

    fn write(&self, buf: &[u8]) -> io::Result<usize> {
        // 获取写锁，确保写操作互斥
        let _write_guard = self
            .write_lock
            .lock()
            .map_err(|_| io::Error::new(io::ErrorKind::Other, "Failed to acquire write lock"))?;

        // 检查逻辑连接状态
        self.check_logical_connection()?;

        // 执行写操作
        let mut inner = self
            .inner
            .lock()
            .map_err(|_| io::Error::new(io::ErrorKind::Other, "Failed to acquire inner lock"))?;

        match inner.as_mut() {
            Some(port) => {
                match port.write(buf) {
                    Ok(size) => {
                        // 尝试刷新缓冲区
                        if let Err(flush_err) = port.flush() {
                            eprintln!("Warning: Failed to flush serial port: {}", flush_err);
                        }
                        Ok(size)
                    }
                    Err(e) => {
                        // 检查是否是连接断开的错误
                        if self.is_connection_error(&e) {
                            // 释放 inner 锁后再调用 mark_disconnected
                            drop(inner);
                            self.mark_disconnected()?;
                            Err(io::Error::new(
                                io::ErrorKind::NotConnected,
                                format!("Serial port disconnected: {}", e),
                            ))
                        } else {
                            Err(e)
                        }
                    }
                }
            }
            None => Err(io::Error::new(
                io::ErrorKind::NotConnected,
                "Serial port is not available",
            )),
        }
    }

    fn close(&self) -> io::Result<()> {
        // 获取所有锁，确保没有其他操作在进行
        let _read_guard = self
            .read_lock
            .lock()
            .map_err(|_| io::Error::new(io::ErrorKind::Other, "Failed to acquire read lock"))?;
        let _write_guard = self
            .write_lock
            .lock()
            .map_err(|_| io::Error::new(io::ErrorKind::Other, "Failed to acquire write lock"))?;

        // 检查是否已经关闭
        {
            let is_open = self.is_open.lock().map_err(|_| {
                io::Error::new(io::ErrorKind::Other, "Failed to acquire is_open lock")
            })?;
            if !*is_open {
                return Ok(()); // 已经关闭，直接返回
            }
        }

        // 关闭串口
        {
            let mut inner = self.inner.lock().map_err(|_| {
                io::Error::new(io::ErrorKind::Other, "Failed to acquire inner lock")
            })?;
            *inner = None; // 释放串口对象，自动调用 Drop
        }

        // 更新状态
        {
            let mut is_open = self.is_open.lock().map_err(|_| {
                io::Error::new(io::ErrorKind::Other, "Failed to acquire is_open lock")
            })?;
            *is_open = false;
        }

        Ok(())
    }

    // 辅助方法：判断是否是连接错误
    fn is_connection_error(&self, error: &io::Error) -> bool {
        match error.kind() {
            io::ErrorKind::BrokenPipe
            | io::ErrorKind::ConnectionAborted
            | io::ErrorKind::ConnectionReset
            | io::ErrorKind::NotConnected => true,
            _ => {
                // 检查错误消息中是否包含常见的断开连接关键词
                let error_msg = error.to_string().to_lowercase();
                error_msg.contains("device not found")
                    || error_msg.contains("no such device")
                    || error_msg.contains("device disconnected")
                    || error_msg.contains("access denied")
            }
        }
    }

    // 辅助方法：标记为断开连接
    fn mark_disconnected(&self) -> io::Result<()> {
        let mut is_open = self
            .is_open
            .lock()
            .map_err(|_| io::Error::new(io::ErrorKind::Other, "Failed to acquire is_open lock"))?;
        *is_open = false;

        let mut inner = self
            .inner
            .lock()
            .map_err(|_| io::Error::new(io::ErrorKind::Other, "Failed to acquire inner lock"))?;
        *inner = None;

        Ok(())
    }

    // 获取连接状态
    fn is_connected(&self) -> bool {
        if let Ok(is_open) = self.is_open.lock() {
            *is_open
        } else {
            false
        }
    }
}
