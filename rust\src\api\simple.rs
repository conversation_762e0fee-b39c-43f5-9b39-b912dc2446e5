use std::{
    io,
    sync::{<PERSON>, Mutex},
    time::Duration,
};

use flutter_rust_bridge::frb;
use serialport::SerialPort;

#[flutter_rust_bridge::frb(sync)] // Synchronous mode for simplicity of the demo
pub fn greet(name: String) -> String {
    format!("Hello, {name}!")
}

#[flutter_rust_bridge::frb(init)]
pub fn init_app() {
    // Default utilities - feel free to customize
    flutter_rust_bridge::setup_default_user_utils();
}

#[frb(unignore)]
pub struct MyStruct {
    pub name: String,
    pub age: u32,
}

impl MyStruct {
    #[frb(sync)]
    pub fn new(name: String, age: u32) -> Self {
        Self { name, age }
    }

    #[frb] // This allows the method to be called synchronously from Dart
    pub fn greet(&self) -> String {
        format!(
            "Hello, my name is {} and I am {} years old.",
            self.name, self.age
        )
    }
}
#[frb(opaque)]
pub struct FlutterRs {
    pub port_name: String,
    pub baud_rate: u32,
    // pub data_bits: u8,
    // pub stop_bits: u8,
    // pub parity: u8,

    // private fields
    _port: Option<Arc<SafeSerialPort>>,
}

impl FlutterRs {
    #[frb(sync)]
    pub fn new(
        port_name: String,
        baud_rate: u32,
        // data_bits: u8,
        // stop_bits: u8,
        // parity: u8,
    ) -> Self {
        Self {
            port_name,
            baud_rate,
            // data_bits,
            // stop_bits,
            // parity,
            _port: None,
        }
    }

    #[frb]
    pub fn open(&mut self) -> io::Result<()> {
        let safe_port = SafeSerialPort::new(&self.port_name, self.baud_rate);
        safe_port.open()?; // 显式调用 open 方法
        self._port = Some(Arc::new(safe_port));
        Ok(())
    }

    #[frb]
    pub fn read(&self) -> io::Result<Vec<u8>> {
        let port = self._port.as_ref().ok_or_else(|| {
            io::Error::new(io::ErrorKind::NotConnected, "Serial port is not open")
        })?;

        let mut buff = vec![0; 1024];

        match port.read(&mut buff) {
            Ok(size) => Ok(buff[..size].to_vec()),
            Err(ref e) if e.kind() == io::ErrorKind::TimedOut => Ok(vec![]),
            Err(e) => Err(e),
        }
    }

    #[frb]
    pub fn write(&self, output: Vec<u8>) -> io::Result<usize> {
        let port = self._port.as_ref().ok_or_else(|| {
            io::Error::new(io::ErrorKind::NotConnected, "Serial port is not open")
        })?;

        port.write(&output)
    }

    #[frb]
    pub fn close(&self) -> io::Result<()> {
        let port = self._port.as_ref().ok_or_else(|| {
            io::Error::new(io::ErrorKind::NotConnected, "Serial port is not open")
        })?;

        port.close()
    }

    #[frb]
    pub fn is_connected(&self) -> bool {
        match &self._port {
            Some(port) => port.is_connected(),
            None => false,
        }
    }
}

#[cfg(test)]
mod tests {

    use chrono::Local;

    use super::*;

    #[test]
    fn test_greet() {
        assert_eq!(greet("Tom".to_string()), "Hello, Tom!");
    }

    #[test]
    fn test_flutter_rs() {
        let mut rs = FlutterRs::new("COM15".to_string(), 115200);
        rs.open().unwrap();

        for _ in 0..100 {
            let buf = rs.read().unwrap();
            println!("time {:?} {:?}", Local::now(), buf);
        }
    }

    #[test]
    fn test_flutter_rs_close() {
        let mut rs = FlutterRs::new("COM15".to_string(), 115200);
        rs.open().unwrap();

        rs.close().unwrap();

        assert!(rs.write(vec![1, 2, 3]).is_err());
    }

    #[test]
    fn test_raw_rs_lib() {
        let ports = serialport::available_ports().expect("No ports found!");
        for p in ports {
            println!("{}", p.port_name);
        }

        let mut port = serialport::new("COM15", 115_200)
            // .timeout(Duration::from_millis(1000))
            .open()
            .unwrap();

        let mut buff = vec![0; 256];
        let size = port.read(&mut buff).unwrap();
        buff.truncate(size);
        println!("{:?}", buff);

        std::mem::drop(port);
    }

    #[test]
    fn test_safe() {
        let port = SafeSerialPort::new("COM15", 115200);
        // 测试创建 SafeSerialPort 实例（此时应该未连接）
        assert!(!port.is_connected());
        println!("SafeSerialPort created successfully (not connected)");

        // 测试打开串口（可能会失败，因为没有实际硬件）
        match port.open() {
            Ok(_) => {
                println!("Serial port opened successfully");
                assert!(port.is_connected());
            }
            Err(e) => {
                println!(
                    "Failed to open serial port (expected without hardware): {}",
                    e
                );
                assert!(!port.is_connected());
            }
        }
    }

    #[test]
    fn test_thread_safety() {
        use std::sync::Arc;
        use std::thread;
        use std::time::Duration;

        let port = Arc::new(SafeSerialPort::new("COM15", 115200));

        // 尝试打开串口（可能会失败，但不影响线程安全测试）
        let _ = port.open();

        let mut handles = vec![];

        // 创建多个读线程
        for i in 0..3 {
            let port_clone = Arc::clone(&port);
            let handle = thread::spawn(move || {
                let mut buf = vec![0; 64];
                for _j in 0..5 {
                    match port_clone.read(&mut buf) {
                        Ok(size) => println!("Read thread {}: read {} bytes", i, size),
                        Err(e) => println!("Read thread {}: error {}", i, e),
                    }
                    thread::sleep(Duration::from_millis(10));
                }
            });
            handles.push(handle);
        }

        // 创建多个写线程
        for i in 0..3 {
            let port_clone = Arc::clone(&port);
            let handle = thread::spawn(move || {
                let data = vec![i as u8; 8];
                for _j in 0..5 {
                    match port_clone.write(&data) {
                        Ok(size) => println!("Write thread {}: wrote {} bytes", i, size),
                        Err(e) => println!("Write thread {}: error {}", i, e),
                    }
                    thread::sleep(Duration::from_millis(10));
                }
            });
            handles.push(handle);
        }

        // 等待所有线程完成
        for handle in handles {
            handle.join().unwrap();
        }

        println!("Thread safety test completed");
    }
}

struct SafeSerialPort {
    inner: Arc<Mutex<Option<Box<dyn SerialPort>>>>,
    read_lock: Arc<Mutex<()>>,
    write_lock: Arc<Mutex<()>>,
    is_open: Arc<Mutex<bool>>,
    name: String,
    baud: u32,
}

impl SafeSerialPort {
    fn new(name: &str, baud: u32) -> Self {
        Self {
            inner: Arc::new(Mutex::new(None)),
            read_lock: Arc::new(Mutex::new(())),
            write_lock: Arc::new(Mutex::new(())),
            is_open: Arc::new(Mutex::new(false)),
            name: name.to_string(),
            baud,
        }
    }

    fn open(&self) -> io::Result<()> {
        // 获取写锁和读锁，确保没有其他操作在进行
        let _read_guard = self
            .read_lock
            .lock()
            .map_err(|_| io::Error::new(io::ErrorKind::Other, "Failed to acquire read lock"))?;
        let _write_guard = self
            .write_lock
            .lock()
            .map_err(|_| io::Error::new(io::ErrorKind::Other, "Failed to acquire write lock"))?;

        // 检查是否已经打开
        {
            let is_open = self.is_open.lock().map_err(|_| {
                io::Error::new(io::ErrorKind::Other, "Failed to acquire is_open lock")
            })?;
            if *is_open {
                return Ok(()); // 已经打开，直接返回
            }
        }

        // 创建串口连接
        let port = serialport::new(&self.name, self.baud)
            .timeout(Duration::from_millis(1000))
            .open()?;

        // 存储串口对象
        {
            let mut inner = self.inner.lock().map_err(|_| {
                io::Error::new(io::ErrorKind::Other, "Failed to acquire inner lock")
            })?;
            *inner = Some(port);
        }

        // 更新状态
        {
            let mut is_open = self.is_open.lock().map_err(|_| {
                io::Error::new(io::ErrorKind::Other, "Failed to acquire is_open lock")
            })?;
            *is_open = true;
        }

        Ok(())
    }

    // 只检查逻辑状态，不获取 inner 锁
    fn check_logical_connection(&self) -> io::Result<()> {
        let is_open = self
            .is_open
            .lock()
            .map_err(|_| io::Error::new(io::ErrorKind::Other, "Failed to acquire is_open lock"))?;

        if !*is_open {
            return Err(io::Error::new(
                io::ErrorKind::NotConnected,
                "Serial port is not open",
            ));
        }

        Ok(())
    }

    fn read(&self, buf: &mut [u8]) -> io::Result<usize> {
        // 获取读锁，确保读操作互斥
        let _read_guard = self
            .read_lock
            .lock()
            .map_err(|_| io::Error::new(io::ErrorKind::Other, "Failed to acquire read lock"))?;

        // 检查逻辑连接状态
        self.check_logical_connection()?;

        // 执行读操作
        let mut inner = self
            .inner
            .lock()
            .map_err(|_| io::Error::new(io::ErrorKind::Other, "Failed to acquire inner lock"))?;

        match inner.as_mut() {
            Some(port) => {
                match port.read(buf) {
                    Ok(size) => Ok(size),
                    Err(e) => {
                        // 检查是否是连接断开的错误
                        if self.is_connection_error(&e) {
                            // 释放 inner 锁后再调用 mark_disconnected
                            drop(inner);
                            self.mark_disconnected()?;
                            Err(io::Error::new(
                                io::ErrorKind::NotConnected,
                                format!("Serial port disconnected: {}", e),
                            ))
                        } else {
                            Err(e)
                        }
                    }
                }
            }
            None => Err(io::Error::new(
                io::ErrorKind::NotConnected,
                "Serial port is not available",
            )),
        }
    }

    fn write(&self, buf: &[u8]) -> io::Result<usize> {
        // 获取写锁，确保写操作互斥
        let _write_guard = self
            .write_lock
            .lock()
            .map_err(|_| io::Error::new(io::ErrorKind::Other, "Failed to acquire write lock"))?;

        // 检查逻辑连接状态
        self.check_logical_connection()?;

        // 执行写操作
        let mut inner = self
            .inner
            .lock()
            .map_err(|_| io::Error::new(io::ErrorKind::Other, "Failed to acquire inner lock"))?;

        match inner.as_mut() {
            Some(port) => {
                match port.write(buf) {
                    Ok(size) => {
                        // 尝试刷新缓冲区
                        if let Err(flush_err) = port.flush() {
                            eprintln!("Warning: Failed to flush serial port: {}", flush_err);
                        }
                        Ok(size)
                    }
                    Err(e) => {
                        // 检查是否是连接断开的错误
                        if self.is_connection_error(&e) {
                            // 释放 inner 锁后再调用 mark_disconnected
                            drop(inner);
                            self.mark_disconnected()?;
                            Err(io::Error::new(
                                io::ErrorKind::NotConnected,
                                format!("Serial port disconnected: {}", e),
                            ))
                        } else {
                            Err(e)
                        }
                    }
                }
            }
            None => Err(io::Error::new(
                io::ErrorKind::NotConnected,
                "Serial port is not available",
            )),
        }
    }

    fn close(&self) -> io::Result<()> {
        // 获取所有锁，确保没有其他操作在进行
        let _read_guard = self
            .read_lock
            .lock()
            .map_err(|_| io::Error::new(io::ErrorKind::Other, "Failed to acquire read lock"))?;
        let _write_guard = self
            .write_lock
            .lock()
            .map_err(|_| io::Error::new(io::ErrorKind::Other, "Failed to acquire write lock"))?;

        // 检查是否已经关闭
        {
            let is_open = self.is_open.lock().map_err(|_| {
                io::Error::new(io::ErrorKind::Other, "Failed to acquire is_open lock")
            })?;
            if !*is_open {
                return Ok(()); // 已经关闭，直接返回
            }
        }

        // 关闭串口
        {
            let mut inner = self.inner.lock().map_err(|_| {
                io::Error::new(io::ErrorKind::Other, "Failed to acquire inner lock")
            })?;
            *inner = None; // 释放串口对象，自动调用 Drop
        }

        // 更新状态
        {
            let mut is_open = self.is_open.lock().map_err(|_| {
                io::Error::new(io::ErrorKind::Other, "Failed to acquire is_open lock")
            })?;
            *is_open = false;
        }

        Ok(())
    }

    // 辅助方法：判断是否是连接错误
    fn is_connection_error(&self, error: &io::Error) -> bool {
        match error.kind() {
            io::ErrorKind::BrokenPipe
            | io::ErrorKind::ConnectionAborted
            | io::ErrorKind::ConnectionReset
            | io::ErrorKind::NotConnected => true,
            _ => {
                // 检查错误消息中是否包含常见的断开连接关键词
                let error_msg = error.to_string().to_lowercase();
                error_msg.contains("device not found")
                    || error_msg.contains("no such device")
                    || error_msg.contains("device disconnected")
                    || error_msg.contains("access denied")
            }
        }
    }

    // 辅助方法：标记为断开连接
    fn mark_disconnected(&self) -> io::Result<()> {
        let mut is_open = self
            .is_open
            .lock()
            .map_err(|_| io::Error::new(io::ErrorKind::Other, "Failed to acquire is_open lock"))?;
        *is_open = false;

        let mut inner = self
            .inner
            .lock()
            .map_err(|_| io::Error::new(io::ErrorKind::Other, "Failed to acquire inner lock"))?;
        *inner = None;

        Ok(())
    }

    // 获取连接状态
    fn is_connected(&self) -> bool {
        if let Ok(is_open) = self.is_open.lock() {
            *is_open
        } else {
            false
        }
    }
}
