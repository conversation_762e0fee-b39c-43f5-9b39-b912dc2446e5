use std::{
    io,
    str::FromStr,
    sync::{Arc, Mutex},
    time::Duration,
};

use flutter_rust_bridge::frb;
use serialport::SerialPort;

#[flutter_rust_bridge::frb(sync)] // Synchronous mode for simplicity of the demo
pub fn greet(name: String) -> String {
    format!("Hello, {name}!")
}

#[flutter_rust_bridge::frb(init)]
pub fn init_app() {
    // Default utilities - feel free to customize
    flutter_rust_bridge::setup_default_user_utils();
}

#[frb(unignore)]
pub struct MyStruct {
    pub name: String,
    pub age: u32,
}

impl MyStruct {
    #[frb(sync)]
    pub fn new(name: String, age: u32) -> Self {
        Self { name, age }
    }

    #[frb] // This allows the method to be called synchronously from Dart
    pub fn greet(&self) -> String {
        format!(
            "Hello, my name is {} and I am {} years old.",
            self.name, self.age
        )
    }
}
#[frb(opaque)]
pub struct FlutterRs {
    pub port_name: String,
    pub baud_rate: u32,
    // pub data_bits: u8,
    // pub stop_bits: u8,
    // pub parity: u8,

    // private fields
    _port: Option<Arc<SafeSerialPort>>,
}

impl FlutterRs {
    #[frb(sync)]
    pub fn new(
        port_name: String,
        baud_rate: u32,
        // data_bits: u8,
        // stop_bits: u8,
        // parity: u8,
    ) -> Self {
        Self {
            port_name,
            baud_rate,
            // data_bits,
            // stop_bits,
            // parity,
            _port: None,
        }
    }

    #[frb]
    pub fn open(&mut self) -> io::Result<()> {
        self._port = Some(Arc::new(SafeSerialPort::new(
            &self.port_name,
            self.baud_rate,
        )));
        Ok(())
    }

    #[frb]
    pub fn read(&self) -> io::Result<Vec<u8>> {
        let port = self._port.clone();

        let mut buff = vec![0; 1024];

        match port.unwrap().read(&mut buff) {
            Ok(size) => Ok(buff[..size].to_vec()),
            Err(ref e) if e.kind() == io::ErrorKind::TimedOut => Ok(vec![]),
            Err(e) => Err(e),
        }
    }

    #[frb]
    pub fn write(&self, output: Vec<u8>) -> io::Result<usize> {
        let port = self._port.clone();

        port.unwrap().write(&output)
    }

    #[frb]
    pub fn close(&self) -> io::Result<()> {
        let port = self._port.clone();
        port.unwrap().close()
    }
}

#[cfg(test)]
mod tests {

    use chrono::Local;

    use super::*;

    #[test]
    fn test_greet() {
        assert_eq!(greet("Tom".to_string()), "Hello, Tom!");
    }

    #[test]
    fn test_flutter_rs() {
        let mut rs = FlutterRs::new("COM15".to_string(), 115200);
        rs.open().unwrap();

        for _ in 0..100 {
            let buf = rs.read().unwrap();
            println!("time {:?} {:?}", Local::now(), buf);
        }
    }

    #[test]
    fn test_flutter_rs_close() {
        let mut rs = FlutterRs::new("COM15".to_string(), 115200);
        rs.open().unwrap();

        rs.close().unwrap();

        assert!(rs.write(vec![1, 2, 3]).is_err());
    }

    #[test]
    fn test_raw_rs_lib() {
        let ports = serialport::available_ports().expect("No ports found!");
        for p in ports {
            println!("{}", p.port_name);
        }

        let mut port = serialport::new("COM15", 115_200)
            // .timeout(Duration::from_millis(1000))
            .open()
            .unwrap();

        let mut buff = vec![0; 256];
        let size = port.read(&mut buff).unwrap();
        buff.truncate(size);
        println!("{:?}", buff);

        std::mem::drop(port);
    }

    #[test]
    fn test_safe() {
        let port = SafeSerialPort::new("COM15", 115200);
    }
}

struct SafeSerialPort {
    inner: Arc<Mutex<Option<Box<dyn SerialPort>>>>,
    read_lock: Arc<Mutex<()>>,
    write_lock: Arc<Mutex<()>>,
    _is_open: Arc<Mutex<bool>>,

    _name: String,
    _baud: u32,
}

impl SafeSerialPort {
    fn new(name: &str, baud: u32) -> Self {
        Self {
            inner: Arc::new(Mutex::new(None)),
            read_lock: Arc::new(Mutex::new(())),
            write_lock: Arc::new(Mutex::new(())),
            _is_open: Arc::new(Mutex::new(false)),
            _name: name.to_string(),
            _baud: baud,
        }
    }

    fn open(&self) -> io::Result<()> {
        let port = serialport::new(&self._name, self._baud)
            .timeout(Duration::from_millis(1000))
            .open()?;

        Ok(())
    }

    fn check_open(&self) -> io::Result<()> {
        if !self._is_open {
            Err(io::Error::new(
                io::ErrorKind::NotFound,
                "Port have been closed",
            ))
        } else {
            Ok(())
        }
    }

    fn read(&self, buf: &mut [u8]) -> io::Result<usize> {
        self.check_open()?;

        let _lock = self.read_lock.lock().unwrap();
        self.inner.lock().unwrap().read(buf)
    }

    fn write(&self, buf: &[u8]) -> io::Result<usize> {
        self.check_open()?;

        let _lock = self.write_lock.lock().unwrap();
        self.inner.lock().unwrap().write(buf)
    }

    fn close(&self) -> io::Result<()> {
        if !self._is_open {
            return Ok(());
        }

        let _lock1 = self.read_lock.lock().unwrap();
        let _lock2 = self.write_lock.lock().unwrap();

        std::mem::drop(self.inner.lock().unwrap());

        Ok(())
    }
}
