{"rustc": 1842507548689473721, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 6120535526081445694, "profile": 2241668132362809309, "path": 17920806599311208807, "deps": [[5700553729601021974, "futures_util", false, 3564904088867750776], [6323537352664534935, "futures_io", false, 8942448177359381601], [7743984271493053878, "futures_core", false, 6152847497263732581], [9961102183257020711, "futures_task", false, 776130990552452549], [16078276589449426613, "futures_channel", false, 5919118341750020836], [18183901555165856195, "futures_sink", false, 14408793191998277609], [18216287767803176676, "futures_executor", false, 11582130135996337937]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-074667a545719045\\dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}