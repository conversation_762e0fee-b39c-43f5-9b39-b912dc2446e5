// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.11.1.

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../frb_generated.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';

// These functions are ignored because they are not marked as `pub`: `new`, `read`, `write`
// These types are ignored because they are neither used by any `pub` functions nor (for structs and enums) marked `#[frb(unignore)]`: `SafeSerialPort`

String greet({required String name}) =>
    RustLib.instance.api.crateApiSimpleGreet(name: name);

// Rust type: RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<FlutterRs>>
abstract class FlutterRs implements RustOpaqueInterface {
  int get baudRate;

  String get portName;

  set baudRate(int baudRate);

  set portName(String portName);

  factory FlutterRs({required String portName, required int baudRate}) =>
      RustLib.instance.api.crateApiSimpleFlutterRsNew(
        portName: portName,
        baudRate: baudRate,
      );

  Future<void> open();

  Future<Uint8List> read();

  Future<BigInt> write({required List<int> output});
}

class MyStruct {
  final String name;
  final int age;

  const MyStruct.raw({required this.name, required this.age});

  Future<String> greet() =>
      RustLib.instance.api.crateApiSimpleMyStructGreet(that: this);

  factory MyStruct({required String name, required int age}) =>
      RustLib.instance.api.crateApiSimpleMyStructNew(name: name, age: age);

  @override
  int get hashCode => name.hashCode ^ age.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MyStruct &&
          runtimeType == other.runtimeType &&
          name == other.name &&
          age == other.age;
}
